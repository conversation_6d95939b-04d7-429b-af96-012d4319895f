[{"工具名称": "SystemInfo", "工具类型": "侦察类", "作用对象": "主机对象", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "1993年7月", "依赖项": "Windows API调用", "描述": "SystemInfo是Windows系统内置的命令行工具，专门用于收集和显示本地或远程计算机的详细系统配置信息。它能够获取操作系统版本、硬件配置、网络设置、安装的补丁信息等关键系统数据，为系统管理员提供全面的系统状态概览。", "应用场景": "系统信息收集、资产清点、漏洞评估", "应用效果": "作为Windows系统管理的基础工具，被广泛用于IT运维和安全评估中。攻击者常利用此工具进行目标系统侦察，收集操作系统版本、补丁级别等信息来寻找潜在的安全漏洞。该工具输出的详细信息为后续的漏洞利用和权限提升提供了重要的情报支持。"}, {"工具名称": "CVE-2017-0144 Exploit", "工具类型": "渗透类", "作用对象": "主机对象", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "2017年4月", "依赖项": "SMB v1协议漏洞", "描述": "CVE-2017-0144是Windows SMB服务器中的一个严重远程代码执行漏洞，也被称为EternalBlue。该漏洞允许攻击者通过发送特制的SMB数据包来触发缓冲区溢出，从而在目标系统上执行任意代码。漏洞影响Windows Vista到Windows 10的多个版本。", "应用场景": "远程代码执行、权限获取、横向移动", "应用效果": "该漏洞被WannaCry和NotPetya等恶意软件大规模利用，造成了全球性的网络安全事件。攻击者可以无需用户交互即可获得目标系统的完全控制权，使其成为最危险的网络攻击向量之一。微软发布紧急补丁后，该漏洞仍被广泛用于针对未打补丁系统的攻击。"}, {"工具名称": "Netstat", "工具类型": "侦察类", "作用对象": "网络连接", "目标平台": "跨平台", "使用平台": "Windows/Linux/Unix", "公开时间": "1983年9月", "依赖项": "TCP/IP协议栈", "描述": "Netstat是一个网络统计命令行工具，专门用于显示网络连接、路由表、接口统计、伪装连接和多播成员等信息。它能够列出当前系统上所有活动的网络连接，包括监听端口、已建立的连接以及相关的进程信息，是网络诊断的基础工具。", "应用场景": "网络连接监控、端口扫描、恶意软件检测", "应用效果": "作为系统管理员和安全分析师的必备工具，用于监控系统网络活动和检测异常连接。攻击者利用此工具可以发现目标系统上运行的服务和开放的端口，为进一步的攻击提供目标信息。该工具在恶意软件分析和网络取证中也发挥着重要作用。"}, {"工具名称": "CVE-2019-0708 <PERSON><PERSON><PERSON>", "工具类型": "渗透类", "作用对象": "远程桌面服务", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "2019年5月", "依赖项": "RDP协议漏洞", "描述": "CVE-2019-0708是Windows远程桌面服务中的一个严重远程代码执行漏洞，被称为BlueKeep。该漏洞存在于RDP协议的实现中，攻击者可以通过发送特制的RDP请求来触发漏洞，无需身份验证即可在目标系统上执行任意代码。", "应用场景": "远程代码执行、蠕虫传播、系统入侵", "应用效果": "该漏洞具有蠕虫传播的潜力，可能导致类似WannaCry的大规模攻击。微软罕见地为已停止支持的Windows XP和Windows 2003发布了安全补丁。虽然大规模利用尚未出现，但该漏洞仍被认为是高危威胁，促使全球组织加强RDP安全防护措施。"}, {"工具名称": "<PERSON><PERSON>", "工具类型": "侦察类", "作用对象": "用户权限", "目标平台": "跨平台", "使用平台": "Windows/Linux/Unix", "公开时间": "1971年11月", "依赖项": "操作系统用户管理", "描述": "Whoami是一个简单的命令行工具，用于显示当前登录用户的用户名和相关权限信息。在Windows系统中，它还可以显示用户的安全标识符(SID)、用户组成员身份和权限级别等详细信息，是权限验证和故障排除的基础工具。", "应用场景": "权限确认、身份验证、权限提升验证", "应用效果": "作为最基础的系统工具之一，被广泛用于确认当前用户身份和权限级别。在渗透测试中，攻击者使用此工具来确认获得的权限级别，判断是否需要进行权限提升。该工具简单但关键，是任何系统管理和安全评估活动的起点。"}, {"工具名称": "CVE-2020-1472 Zerologon", "工具类型": "渗透类", "作用对象": "域控制器", "目标平台": "x86/x64", "使用平台": "Windows Server", "公开时间": "2020年8月", "依赖项": "Netlogon协议漏洞", "描述": "CVE-2020-1472是Windows Netlogon远程协议中的一个严重权限提升漏洞，被称为Zerologon。该漏洞允许攻击者通过发送特制的Netlogon消息来重置域控制器的计算机账户密码，从而获得域管理员权限。", "应用场景": "域权限提升、Active Directory攻击、企业网络入侵", "应用效果": "该漏洞被评为CVSS 10.0满分，是近年来最严重的Windows漏洞之一。攻击者可以在几秒钟内完全控制整个Active Directory域，获得企业网络的最高权限。该漏洞的发现促使全球企业紧急更新域控制器，并重新评估Active Directory的安全架构。"}, {"工具名称": "Tasklist", "工具类型": "侦察类", "作用对象": "进程信息", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "2000年2月", "依赖项": "Windows进程管理API", "描述": "Tasklist是Windows系统内置的命令行工具，专门用于显示本地或远程计算机上当前运行的进程列表。它能够显示进程ID、进程名称、会话名称、会话编号、内存使用情况等详细信息，支持多种过滤和格式化选项。", "应用场景": "进程监控、恶意软件检测、系统性能分析", "应用效果": "作为系统管理的基础工具，被广泛用于监控系统进程状态和检测异常活动。攻击者利用此工具可以发现目标系统上运行的安全软件、服务进程等关键信息，为规避检测和选择攻击目标提供依据。该工具在数字取证和恶意软件分析中也是不可或缺的。"}, {"工具名称": "CVE-2021-44228 Log4Shell", "工具类型": "渗透类", "作用对象": "Java应用程序", "目标平台": "跨平台", "使用平台": "Java虚拟机", "公开时间": "2021年12月", "依赖项": "Log4j库JNDI注入漏洞", "描述": "CVE-2021-44228是Apache Log4j日志库中的一个严重远程代码执行漏洞，被称为Log4Shell。该漏洞允许攻击者通过在日志消息中注入特制的JNDI查找字符串来触发远程代码执行，影响全球数百万个Java应用程序。", "应用场景": "远程代码执行、Web应用攻击、企业系统入侵", "应用效果": "该漏洞被认为是近十年来最严重的安全漏洞之一，影响范围极其广泛，包括云服务、企业应用、游戏服务器等。攻击者可以通过简单的HTTP请求触发漏洞，获得目标系统的完全控制权。该漏洞的发现引发了全球性的紧急响应，促使整个软件行业重新审视供应链安全。"}, {"工具名称": "Ipconfig", "工具类型": "侦察类", "作用对象": "网络配置", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "1993年7月", "依赖项": "TCP/IP协议栈", "描述": "Ipconfig是Windows系统内置的网络配置命令行工具，专门用于显示和管理网络接口的IP配置信息。它能够显示IP地址、子网掩码、默认网关、DNS服务器等网络参数，还支持释放和更新DHCP租约等网络管理功能。", "应用场景": "网络配置查看、网络故障排除、网络侦察", "应用效果": "作为网络管理的基础工具，被系统管理员广泛用于网络配置和故障诊断。攻击者利用此工具可以快速了解目标系统的网络环境，包括内网IP段、DNS服务器、网关信息等，为后续的网络攻击和横向移动提供重要的网络拓扑信息。"}, {"工具名称": "CVE-2022-30190 Follina", "工具类型": "渗透类", "作用对象": "Microsoft Office", "目标平台": "x86/x64", "使用平台": "Windows", "公开时间": "2022年5月", "依赖项": "MSDT协议漏洞", "描述": "CVE-2022-30190是Microsoft Office中的一个远程代码执行漏洞，被称为Follina。该漏洞存在于Microsoft支持诊断工具(MSDT)中，攻击者可以通过恶意的Office文档触发该漏洞，无需启用宏即可在目标系统上执行任意代码。", "应用场景": "钓鱼攻击、文档恶意利用、初始访问获取", "应用效果": "该漏洞特别危险，因为它绕过了Office的宏安全机制，使得攻击者可以通过看似无害的Word文档进行攻击。该漏洞被广泛用于针对性攻击和钓鱼活动中，促使微软加强了Office文档的安全检查机制。攻击者利用此漏洞可以轻易获得目标系统的初始访问权限。"}]